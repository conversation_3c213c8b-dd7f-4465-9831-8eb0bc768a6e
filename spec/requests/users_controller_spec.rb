require "rails_helper"

RSpec.describe UsersController, type: :request do
  let(:admin_user) { create(:user, :super_admin) }
  let(:dealership) { create(:dealership) }
  
  before do
    sign_in admin_user
  end

  describe "POST /users" do
    let(:valid_attributes) do
      {
        user: {
          first_name: "<PERSON>",
          last_name: "<PERSON><PERSON>", 
          email: "<EMAIL>",
          phone: "+***********",
          user_type: "dealership_user",
          status: "active",
          job_title: "Sales Manager",
          preferred_language: "english",
          external_id: "EXT123",
          time_zone: "Australia/Sydney",
          password_change_required: true,
          onboarding_completed: false,
          driver_license_attributes: {
            licence_number: "12345678",
            full_name: "<PERSON>",
            date_of_birth: "1990-01-01",
            expiry_date: "2025-12-31",
            issue_date: "2020-01-01",
            category: "C",
            issuing_country: "au",
            issuing_state: "NSW",
            verification_status: "pending"
          }
        }
      }
    end

    context "with valid parameters" do
      it "creates a new user with all fields" do
        expect {
          post users_path, params: valid_attributes
        }.to change(User, :count).by(1)
        
        user = User.last
        expect(user.first_name).to eq("<PERSON>")
        expect(user.last_name).to eq("Doe")
        expect(user.email).to eq("<EMAIL>")
        expect(user.phone).to eq("+***********")
        expect(user.user_type).to eq("dealership_user")
        expect(user.status).to eq("active")
        expect(user.job_title).to eq("Sales Manager")
        expect(user.preferred_language).to eq("english")
        expect(user.external_id).to eq("EXT123")
        expect(user.time_zone).to eq("Australia/Sydney")
        expect(user.password_change_required).to be true
        expect(user.onboarding_completed).to be false
        
        # Check driver license
        expect(user.driver_license).to be_present
        expect(user.driver_license.licence_number).to eq("12345678")
        expect(user.driver_license.full_name).to eq("John Doe")
        expect(user.driver_license.verification_status).to eq("pending")
      end

      it "redirects to the user show page" do
        post users_path, params: valid_attributes
        expect(response).to redirect_to(user_path(User.last))
      end
    end

    context "with custom password" do
      let(:password_attributes) do
        valid_attributes.deep_merge(
          user: {
            password: "CustomPass@123",
            password_confirmation: "CustomPass@123"
          }
        )
      end

      it "uses the provided password instead of generating one" do
        post users_path, params: password_attributes
        user = User.last
        expect(user.valid_password?("CustomPass@123")).to be true
      end
    end
  end

  describe "PATCH /users/:id" do
    let(:user) { create(:user) }
    let(:update_attributes) do
      {
        user: {
          job_title: "Updated Title",
          external_id: "UPDATED123",
          preferred_language: "spanish",
          onboarding_completed: true
        }
      }
    end

    it "updates the user with new fields" do
      patch user_path(user), params: update_attributes
      
      user.reload
      expect(user.job_title).to eq("Updated Title")
      expect(user.external_id).to eq("UPDATED123")
      expect(user.preferred_language).to eq("spanish")
      expect(user.onboarding_completed).to be true
    end

    it "redirects to the user show page" do
      patch user_path(user), params: update_attributes
      expect(response).to redirect_to(user_path(user))
    end
  end
end
