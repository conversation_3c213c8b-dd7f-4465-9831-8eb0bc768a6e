<% content_for(:body_class, 'users') %>
<% content_for(:title, @user.name) %>
<% content_for(:section_heading, @user.name) %>
<main class="main">
  <div class="section-forms">
    <%= render 'shared/bread_crumb', crumbs: [
      { step: "Users", link: users_path },
      { step: "#{@user.name}" }
    ]
    %>
    <div class="section-users box-trbl">
      <ul class="list-recurring list-user-edit">
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">First Name</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= @user.first_name %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Last Name</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= @user.last_name %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Email Address</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= mail_to @user.email %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Phone</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= @user.formatted_phone || @user.phone %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Job Title</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= @user.job_title.presence || "&mdash;".html_safe %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">External ID</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= @user.external_id.presence || "&mdash;".html_safe %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Time zone</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><% if @user.time_zone %><%= "(GMT#{TimeZone.offset(@user.time_zone)}) #{@user.time_zone}" %><% else %> &mdash; <% end %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">User Access</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= @user.user_type.humanize %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Status</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= @user.status.humanize %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Preferred Language</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= @user.preferred_language.humanize %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Password Change Required</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= @user.password_change_required? ? "Yes" : "No" %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Onboarding Completed</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= @user.onboarding_completed? ? "Yes" : "No" %></span></div>
        </li>
        <% if @user.photo.attached? %>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Photo</span></div>
          <div class="lr-col lr-col-lg">
            <%= image_tag @user.photo, alt: "#{@user.name} photo", style: "max-width: 150px; max-height: 150px;" %>
          </div>
        </li>
        <% end %>
        <% if @user.driver_license.present? %>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Driver License</span></div>
          <div class="lr-col lr-col-lg">
            <span class="lr-text">
              <%= @user.driver_license.licence_number %><br>
              <small>Expires: <%= @user.driver_license.expiry_date %></small><br>
              <small>Status: <%= @user.driver_license.verification_status.humanize %></small>
            </span>
          </div>
        </li>
        <% end %>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Last Active</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= user_last_active_on(@user.last_active_on) %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md">
            <%= link_to(edit_user_path(@user), class: 'lr-action-wrapper') do %>
              <%= use_svg('lr-action-icon icon-pencil-edit-line', 'pencil-edit-line') %><span class="lr-text lr-action-text">Edit User</span>
            <% end %>
          </div>
          <div class="lr-col lr-col-lg">
            <span class="lr-text">Edit user details</span>
          </div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md">
            <%= link_to(user_path(@user), data: { turbo_method: :delete, turbo_confirm: "Are you sure you want to permanently delete #{@user.name}?" }, class: 'lr-action-wrapper') do %>
              <%= use_svg('lr-action-icon icon-trash', 'trash') %><span class="lr-text lr-action-text">Delete User</span>
            <% end %>
          </div>
          <div class="lr-col lr-col-lg">
            <span class="lr-text">Delete this user</span>
          </div>
        </li>
      </ul>
    </div>
  </div>
</main>
