<% content_for(:body_class, 'users') %>
<% content_for(:title, @user.name) %>
<% content_for(:section_heading, @user.name) %>
<main class="main">
  <div class="section-forms">
    <%= render 'shared/bread_crumb', crumbs: [
      { step: "Users", link: users_path },
      { step: "#{@user.name}" }
    ]
    %>
    <div class="section-users box-trbl">
      <ul class="list-recurring list-user-edit">
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">First Name</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= @user.first_name %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Last Name</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= @user.last_name %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Email Address</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= mail_to @user.email %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Time zone</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><% if @user.time_zone %><%= "(GMT#{TimeZone.offset(@user.time_zone)}) #{@user.time_zone}" %><% else %> &mdash; <% end %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">User Access</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= @user.user_type.humanize %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Last Active</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= user_last_active_on(@user.last_active_on) %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md">
            <%= link_to(edit_user_path(@user), class: 'lr-action-wrapper') do %>
              <%= use_svg('lr-action-icon icon-pencil-edit-line', 'pencil-edit-line') %><span class="lr-text lr-action-text">Edit User</span>
            <% end %>
          </div>
          <div class="lr-col lr-col-lg">
            <span class="lr-text">Edit user details</span>
          </div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md">
            <%= link_to(user_path(@user), data: { turbo_method: :delete, turbo_confirm: "Are you sure you want to permanently delete #{@user.name}?" }, class: 'lr-action-wrapper') do %>
              <%= use_svg('lr-action-icon icon-trash', 'trash') %><span class="lr-text lr-action-text">Delete User</span>
            <% end %>
          </div>
          <div class="lr-col lr-col-lg">
            <span class="lr-text">Delete this user</span>
          </div>
        </li>
      </ul>
    </div>
  </div>
</main>
