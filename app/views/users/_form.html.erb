<%= form_for @user, builder: CmsFormBuilder do |f| %>
  <%= render "shared/errors", object: @user %>
  <fieldset>
    <div class="col-wrapper-2">
      <div class="imf-input-wrapper">
        <%= f.label :first_name, nil, mandatory: true %>
        <%= f.text_field :first_name, autofocus: true %>
      </div>
      <div class="imf-input-wrapper">
        <%= f.label :last_name, nil, mandatory: true %>
        <%= f.text_field :last_name %>
      </div>
      <div class="imf-input-wrapper">
        <%= f.label :email, nil, mandatory: true %>
        <%= f.email_field :email %>
      </div>
      <div class="imf-input-wrapper">
        <%= f.label :phone, nil, mandatory: true %>
        <%= f.text_field :phone %>
      </div>
      <div class="imf-input-wrapper">
        <%= f.label :user_type %>
        <%= f.select :user_type, options_for_select(User.user_types.collect { |user_type| [user_type[0].humanize.titleize, user_type[0]] }, selected: @user.user_type) %>
      </div>
      <div class="imf-input-wrapper">
        <%= f.label :time_zone %>
        <%= f.select :time_zone, ActiveSupport::TimeZone.all.map(&:name), prompt: true %>
      </div>
    </div>
    <%= render 'shared/cancel_and_save_buttons', cancellation_path: cancellation_path, f: f, button_name: button_name %>
  </fieldset>
<% end %>
