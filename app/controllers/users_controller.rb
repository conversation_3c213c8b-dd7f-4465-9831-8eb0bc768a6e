class UsersController < AdminController
  before_action :set_user, only: [ :show, :edit, :update, :destroy ]

  def index
    @users = User.order(:first_name, :last_name)
  end

  def show
  end

  def new
    @user = User.new
    @dealerships = Dealership.active.order(:name)
  end

  def edit
    @dealerships = Dealership.active.order(:name)
  end
  def create
    @user = User.new(user_params)
    @user.password = generate_temp_password
    @user.password_confirmation = @user.password
    @user.password_change_required = true

    if @user.save
      create_dealership_associations
      redirect_to user_path(@user), notice: "User created successfully."
    else
      @dealerships = Dealership.active.order(:name)
      render :new
    end
  end


  def update
    if @user.update(user_params)
      update_dealership_associations
      redirect_to user_path(@user), notice: "User updated successfully."
    else
      @dealerships = Dealership.active.order(:name)
      render :edit
    end
  end

  def destroy
    @user.update!(
      status: :deleted,
    )
    redirect_to users_path, notice: "User deleted successfully."
  end

  private

  def set_user
    @user = User.find_by(uuid: params[:id])
  end

  def user_params
    params.expect(user: [ :email, :first_name, :last_name, :phone, :user_type, :status ])
  end

  def dealership_params
    params.expect(user: [ dealership_roles: {} ])
  end

  def generate_temp_password
    SecureRandom.alphanumeric(12) + "!"
  end

  def create_dealership_associations
    return if params[:user][:dealership_roles].blank?

    params[:user][:dealership_roles].each do |dealership_id, role|
      next if role.blank?

      @user.user_dealerships.create!(
        dealership_id: dealership_id,
        role: role
      )
    end
  end

  def update_dealership_associations
    @user.user_dealerships.destroy_all
    create_dealership_associations
  end
end
